"""
Test Complete Progressive Implementation with ALL 150+ Indicators

This script tests the complete progressive implementation that includes:
- All momentum indicators (35+)
- All overlap indicators (30+) 
- All volatility indicators (7+)
- All volume indicators (15+)
- All trend indicators (20+)
- All statistics indicators (10+)
- All utility indicators (10+)
- All 62 candle patterns

Total: 150+ indicators with exact pandas-ta defaults
"""

import pandas as pd
import numpy as np
import pandas_ta as ta
from datetime import datetime, timedelta
import logging
from complete_progressive_indicators_calculator import CompleteProgressiveIndicatorsCalculator

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_professional_test_data(num_candles=200, interval_minutes=5):
    """
    Create professional-grade test data with realistic market patterns
    """
    logger.info(f"📊 Creating {num_candles} candles of {interval_minutes}-minute professional test data")
    
    # Create time series
    start_time = datetime(2025, 6, 24, 9, 15)
    times = [start_time + timedelta(minutes=i * interval_minutes) for i in range(num_candles)]
    
    # Create realistic market phases
    np.random.seed(42)  # For reproducible results
    
    base_price = 1200
    
    # Phase 1: Opening volatility (first 30 candles)
    phase1_length = 30
    phase1 = np.random.normal(0, 8, phase1_length)  # High volatility opening
    
    # Phase 2: Trending phase (next 60 candles)
    phase2_length = 60
    trend_strength = np.linspace(0.5, 2.0, phase2_length)
    phase2 = np.cumsum(np.random.normal(trend_strength, 3))
    
    # Phase 3: Consolidation (next 50 candles)
    phase3_length = 50
    phase3 = np.random.normal(0, 4, phase3_length)
    
    # Phase 4: Breakout and continuation (remaining candles)
    phase4_length = num_candles - phase1_length - phase2_length - phase3_length
    breakout_momentum = np.linspace(1.5, 0.5, phase4_length)
    phase4 = np.cumsum(np.random.normal(breakout_momentum, 2.5))
    
    # Combine all phases
    price_changes = np.concatenate([phase1, phase2, phase3, phase4])
    close_prices = base_price + np.cumsum(price_changes)
    
    # Ensure realistic price bounds
    close_prices = np.maximum(close_prices, base_price * 0.7)
    close_prices = np.minimum(close_prices, base_price * 1.5)
    
    # Generate professional OHLC data
    data = []
    for i, close in enumerate(close_prices):
        # Generate realistic OHLC relationships
        open_price = close_prices[i-1] if i > 0 else base_price
        
        # Calculate realistic high/low based on volatility
        candle_range = abs(np.random.normal(0, 6))
        direction = 1 if close > open_price else -1
        
        # High and low with proper relationships
        if direction > 0:  # Bullish candle
            high = max(open_price, close) + candle_range * 0.7
            low = min(open_price, close) - candle_range * 0.3
        else:  # Bearish candle
            high = max(open_price, close) + candle_range * 0.3
            low = min(open_price, close) - candle_range * 0.7
        
        # Ensure OHLC validity
        high = max(high, open_price, close)
        low = min(low, open_price, close)
        
        # Professional volume calculation
        price_movement = abs(close - open_price)
        volatility = high - low
        base_volume = 2500
        volume_multiplier = 1 + (price_movement / base_price) * 10 + (volatility / base_price) * 5
        volume = int(base_volume * volume_multiplier + np.random.normal(0, 300))
        volume = max(volume, 500)
        
        data.append({
            'time': times[i],
            'Open': round(open_price, 2),
            'High': round(high, 2),
            'Low': round(low, 2),
            'Close': round(close, 2),
            'Volume': volume
        })
    
    df = pd.DataFrame(data)
    logger.info(f"✅ Created professional test data: {len(df)} candles from {df['time'].iloc[0]} to {df['time'].iloc[-1]}")
    return df

def test_all_categories_comprehensive():
    """
    Test ALL categories with comprehensive indicator coverage
    """
    print("🚀 COMPREHENSIVE TEST: ALL 150+ INDICATORS WITH COMPLETE PROGRESSIVE CALCULATION")
    print("=" * 90)
    
    # Create professional test data
    market_data = create_professional_test_data(num_candles=180, interval_minutes=5)
    
    # Test time periods (spread across different market phases)
    test_times = ['10:00', '11:30', '13:00', '14:30', '15:45']
    
    print(f"\n📊 Testing with {len(test_times)} time periods across market phases: {test_times}")
    print("-" * 70)
    
    # Initialize complete progressive calculator
    calculator = CompleteProgressiveIndicatorsCalculator()
    
    # Test all categories
    all_categories = ['momentum', 'overlap', 'volatility', 'volume', 'trend', 'statistics', 'utility']
    
    total_indicators_tested = 0
    total_indicators_with_variation = 0
    category_results = {}
    
    for category in all_categories:
        print(f"\n📂 TESTING {category.upper()} INDICATORS")
        print("-" * 60)
        
        # Calculate progressive indicators for this category
        progressive_results = calculator.calculate_all_indicators_progressive(
            market_data, test_times, interval_minutes=5, categories=[category], method='categories'
        )
        
        if not progressive_results:
            print(f"❌ No results for {category} category")
            continue
        
        # Analyze results
        category_indicators = set()
        for time_str, result_data in progressive_results.items():
            category_indicators.update(result_data['indicators'].keys())
        
        # Remove price-based indicators for analysis
        analysis_indicators = {ind for ind in category_indicators 
                             if ind not in ['CURRENT_PRICE', 'PRICE_CHANGE', 'PRICE_CHANGE_PCT', 'ERROR']}
        
        print(f"📊 Found {len(analysis_indicators)} {category} indicators")
        
        # Test each indicator for progressive values
        indicators_with_variation = 0
        indicators_tested = 0
        
        for indicator_name in sorted(analysis_indicators):
            # Collect values across time periods
            values = []
            for time_str in test_times:
                if time_str in progressive_results:
                    indicators = progressive_results[time_str]['indicators']
                    if indicator_name in indicators:
                        value = indicators[indicator_name]
                        if value is not None and not pd.isna(value):
                            values.append(float(value))
            
            if len(values) >= 3:  # Need at least 3 values to test variation
                indicators_tested += 1
                unique_values = len(set([round(v, 8) for v in values]))  # Round to avoid floating point issues
                
                if unique_values > 1:
                    indicators_with_variation += 1
                    print(f"   ✅ {indicator_name}: {unique_values} unique values")
                else:
                    print(f"   ⚠️ {indicator_name}: Same value across periods")
        
        # Category summary
        if indicators_tested > 0:
            success_rate = (indicators_with_variation / indicators_tested) * 100
            print(f"\n📈 {category.upper()} SUMMARY:")
            print(f"   Indicators found: {len(analysis_indicators)}")
            print(f"   Indicators tested: {indicators_tested}")
            print(f"   With variation: {indicators_with_variation}")
            print(f"   Success rate: {success_rate:.1f}%")
            
            category_results[category] = {
                'found': len(analysis_indicators),
                'tested': indicators_tested,
                'with_variation': indicators_with_variation,
                'success_rate': success_rate
            }
            
            total_indicators_tested += indicators_tested
            total_indicators_with_variation += indicators_with_variation
            
            if success_rate >= 90:
                print(f"   🎯 EXCELLENT: Outstanding progressive calculation!")
            elif success_rate >= 75:
                print(f"   ✅ VERY GOOD: Most indicators show variation")
            elif success_rate >= 50:
                print(f"   ⚠️ GOOD: Many indicators show variation")
            else:
                print(f"   ❌ NEEDS IMPROVEMENT: Few indicators show variation")
        else:
            print(f"   ⚠️ No indicators could be tested for {category}")
    
    # Overall summary
    print(f"\n\n🎯 OVERALL COMPREHENSIVE SUMMARY")
    print("=" * 90)
    
    if total_indicators_tested > 0:
        overall_success_rate = (total_indicators_with_variation / total_indicators_tested) * 100
        
        print(f"📊 TOTAL INDICATORS TESTED: {total_indicators_tested}")
        print(f"✅ INDICATORS WITH VARIATION: {total_indicators_with_variation}")
        print(f"📈 OVERALL SUCCESS RATE: {overall_success_rate:.1f}%")
        
        print(f"\n📂 CATEGORY BREAKDOWN:")
        for category, results in category_results.items():
            print(f"   {category.upper():12}: {results['with_variation']:3d}/{results['tested']:3d} ({results['success_rate']:5.1f}%)")
        
        if overall_success_rate >= 85:
            print(f"\n🎉 OUTSTANDING SUCCESS!")
            print(f"   The complete progressive implementation is working excellently!")
            print(f"   Professional traders can rely on this for accurate technical analysis.")
        elif overall_success_rate >= 70:
            print(f"\n✅ VERY GOOD SUCCESS!")
            print(f"   The implementation works well for most indicators.")
        elif overall_success_rate >= 50:
            print(f"\n⚠️ MODERATE SUCCESS")
            print(f"   The implementation works for many indicators but needs refinement.")
        else:
            print(f"\n❌ NEEDS SIGNIFICANT IMPROVEMENT")
            print(f"   The implementation requires major fixes.")
    
    return category_results

def test_strategy_all_method():
    """
    Test the 'all' method that should include ALL indicators and candle patterns
    """
    print(f"\n\n🌟 TESTING 'ALL' METHOD (Strategy All)")
    print("=" * 90)
    
    # Create test data
    market_data = create_professional_test_data(num_candles=120, interval_minutes=5)
    
    # Test times
    test_times = ['11:00', '13:00', '15:00']
    
    # Initialize calculator
    calculator = CompleteProgressiveIndicatorsCalculator()
    
    # Test 'all' method
    progressive_results = calculator.calculate_all_indicators_progressive(
        market_data, test_times, interval_minutes=5, categories=None, method='all'
    )
    
    if progressive_results:
        # Analyze results
        all_indicators = set()
        for time_str, result_data in progressive_results.items():
            all_indicators.update(result_data['indicators'].keys())
        
        # Categorize indicators
        momentum_indicators = {ind for ind in all_indicators if any(x in ind.lower() for x in ['rsi', 'macd', 'mom', 'roc', 'stoch', 'cci'])}
        overlap_indicators = {ind for ind in all_indicators if any(x in ind.lower() for x in ['sma', 'ema', 'bb', 'kc', 'ichimoku'])}
        volume_indicators = {ind for ind in all_indicators if any(x in ind.lower() for x in ['obv', 'mfi', 'ad', 'cmf', 'eom'])}
        candle_indicators = {ind for ind in all_indicators if 'cdl' in ind.lower()}
        
        print(f"📊 STRATEGY 'ALL' RESULTS:")
        print(f"   Total indicators: {len(all_indicators)}")
        print(f"   Momentum indicators: {len(momentum_indicators)}")
        print(f"   Overlap indicators: {len(overlap_indicators)}")
        print(f"   Volume indicators: {len(volume_indicators)}")
        print(f"   Candle patterns: {len(candle_indicators)}")
        
        # Test variation in key indicators
        key_indicators = ['RSI_14', 'SMA_10', 'EMA_10', 'MACD_12_26_9', 'ATR_14']
        
        print(f"\n🔍 KEY INDICATORS VARIATION TEST:")
        for indicator in key_indicators:
            matching_indicators = [ind for ind in all_indicators if indicator.lower() in ind.lower()]
            if matching_indicators:
                test_indicator = matching_indicators[0]
                values = []
                for time_str in test_times:
                    if time_str in progressive_results:
                        indicators = progressive_results[time_str]['indicators']
                        if test_indicator in indicators:
                            value = indicators[test_indicator]
                            if value is not None and not pd.isna(value):
                                values.append(float(value))
                
                if len(values) >= 2:
                    unique_values = len(set([round(v, 6) for v in values]))
                    if unique_values > 1:
                        print(f"   ✅ {test_indicator}: Different values - {[f'{v:.3f}' for v in values]}")
                    else:
                        print(f"   ❌ {test_indicator}: Same values - {values[0]:.3f}")
                else:
                    print(f"   ⚠️ {test_indicator}: Insufficient data")
            else:
                print(f"   ❌ {indicator}: Not found")
    
    else:
        print(f"❌ No results from 'all' method")

def main():
    """Main comprehensive test function"""
    try:
        print("🚀 COMPLETE PROGRESSIVE INDICATORS IMPLEMENTATION TEST")
        print("=" * 90)
        print("Testing ALL 150+ pandas-ta indicators with progressive calculation")
        print("using exact default parameters from official documentation.")
        print()
        
        # Test all categories comprehensively
        category_results = test_all_categories_comprehensive()
        
        # Test 'all' method
        test_strategy_all_method()
        
        print(f"\n\n🎯 FINAL ASSESSMENT:")
        print("=" * 90)
        print("✅ If most categories show >80% success rate:")
        print("   The complete progressive implementation is ready for professional use!")
        print()
        print("⚠️ If some categories show <80% success rate:")
        print("   Those specific categories may need parameter adjustments.")
        print()
        print("🎉 This implementation provides the foundation for:")
        print("   • Leading indicator analysis")
        print("   • AI/ML feature extraction")
        print("   • Professional trading strategies")
        print("   • Multi-timeframe analysis")
        
    except Exception as e:
        logger.error(f"❌ Test failed: {str(e)}")
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    main()
